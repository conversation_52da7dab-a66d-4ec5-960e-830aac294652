# OMS - Order Management System

A comprehensive Order Management System built with HTML, CSS, and JavaScript.

## Features

### Order Status Management
- **New Orders**: Create new orders with shopping cart functionality
- **Open Orders**: View and manage orders in progress
- **Received Orders**: Track successfully received orders
- **Rejected Orders**: Monitor orders that were rejected with reasons
- **Failed Orders**: Handle orders that failed due to system errors
- **Closed Orders**: Archive of completed or cancelled orders

### Shopping Cart
- Add items to cart manually or from product catalog
- Adjust quantities with +/- buttons
- Remove items from cart
- Real-time total calculation
- Proceed to order functionality

### Order Management
- Dynamic order status changes
- Search functionality across all order types
- Filter orders by priority, reason, or status
- Order details with customer information, totals, and dates

### User Interface
- Clean, professional design with primary color #0e3d77
- Responsive layout for desktop and mobile
- Tab-based navigation
- Modal dialogs for adding items
- Real-time notifications

## File Structure

```
├── index.html          # Main HTML structure
├── styles.css          # CSS styles with CSS variables
├── script.js           # JavaScript functionality
└── README.md           # This file
```

## Getting Started

1. Open `index.html` in a web browser
2. The application will load with sample data
3. Navigate between different order status tabs
4. Use the "New Orders" tab to:
   - Add items to cart using "Add Item to Cart" button
   - Add products from the catalog
   - Proceed to create orders

## Key Components

### CSS Variables
- Primary color: `#0e3d77`
- Text color: `#ffffff` (white)
- Responsive design with mobile-first approach
- Consistent spacing and typography

### JavaScript Classes
- `OMSApp`: Main application class
- Cart management methods
- Order status tracking
- Search and filter functionality
- Modal handling

### Features in Detail

#### Cart Functionality
- Add custom items with name, price, and quantity
- Add predefined products from catalog
- Update quantities dynamically
- Remove items
- Calculate totals automatically

#### Order Processing
- Convert cart items to orders
- Assign unique order IDs
- Set initial status as "open"
- Track creation dates and customer information

#### Status Management
- Orders can be moved between different status categories
- Each status has distinct styling and color coding
- Filter and search within each status category

## Browser Compatibility

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Responsive design for mobile devices
- No external dependencies required

## Customization

### Colors
Modify CSS variables in `styles.css`:
```css
:root {
    --primary-color: #0e3d77;
    --text-color: #ffffff;
    /* Add more custom colors */
}
```

### Products
Update the products array in `script.js`:
```javascript
this.products = [
    { id: 1, name: 'Product Name', price: 99.99, category: 'Category' },
    // Add more products
];
```

### Order Fields
Extend the order object structure in the `proceedToOrder()` method to include additional fields as needed.

## Future Enhancements

- Backend integration for data persistence
- User authentication and authorization
- Advanced reporting and analytics
- Email notifications
- Inventory management integration
- Payment processing
- Order tracking with shipping information

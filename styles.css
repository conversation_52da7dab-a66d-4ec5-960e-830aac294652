/* CSS Variables */
:root {
    --primary-color: #0e3d77;
    --primary-light: #1a4d87;
    --primary-dark: #0a2d5a;
    --text-color: #ffffff;
    --text-secondary: #e0e0e0;
    --background-color: #f5f5f5;
    --card-background: #ffffff;
    --border-color: #ddd;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background-color);
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background-color: var(--primary-color);
    color: var(--text-color);
    padding: 1rem 0;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
}

.header h1 {
    font-size: 2rem;
    font-weight: 600;
}

.user-info {
    margin-top: 0.5rem;
    color: var(--text-secondary);
}

/* Navigation Tabs */
.nav-tabs {
    display: flex;
    background-color: var(--card-background);
    border-radius: 8px;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
    overflow: hidden;
}

.tab-btn {
    flex: 1;
    padding: 1rem;
    border: none;
    background-color: transparent;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background-color: #f8f9fa;
    color: var(--primary-color);
}

.tab-btn.active {
    background-color: var(--primary-color);
    color: var(--text-color);
    border-bottom-color: var(--primary-dark);
}

/* Main Content */
.main-content {
    min-height: 600px;
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: var(--card-background);
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.content-header h2 {
    color: var(--primary-color);
    font-size: 1.5rem;
}

/* Buttons */
.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-color);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-light);
    transform: translateY(-1px);
    box-shadow: var(--shadow-hover);
}

.btn-primary:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    background-color: #6c757d;
    color: var(--text-color);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background-color: #5a6268;
    transform: translateY(-1px);
}

/* Cart Section */
.cart-section {
    background-color: var(--card-background);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
}

.cart-section h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.cart-items {
    min-height: 100px;
    margin-bottom: 1rem;
}

.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 0.5rem;
    background-color: #f8f9fa;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-weight: 600;
    color: var(--primary-color);
}

.cart-item-details {
    color: #666;
    font-size: 0.9rem;
}

.cart-item-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-btn {
    background-color: var(--primary-color);
    color: var(--text-color);
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}

.quantity-display {
    min-width: 30px;
    text-align: center;
    font-weight: 600;
}

.remove-btn {
    background-color: var(--danger-color);
    color: var(--text-color);
    border: none;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
}

.cart-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 2px solid var(--border-color);
}

.cart-total {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* Products Section */
.products-section {
    background-color: var(--card-background);
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.products-section h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

.product-card {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.product-name {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.product-price {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--success-color);
    margin-bottom: 1rem;
}

/* Filters */
.filters {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-input, .filter-select, .date-filter {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.9rem;
}

.search-input {
    min-width: 200px;
}

/* Orders List */
.orders-list {
    background-color: var(--card-background);
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.3s ease;
}

.order-item:hover {
    background-color: #f8f9fa;
}

.order-item:last-child {
    border-bottom: none;
}

.order-info {
    flex: 1;
}

.order-id {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.order-details {
    color: #666;
    font-size: 0.9rem;
}

.order-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-new { background-color: var(--info-color); color: var(--text-color); }
.status-open { background-color: var(--warning-color); color: #333; }
.status-received { background-color: var(--success-color); color: var(--text-color); }
.status-rejected { background-color: var(--danger-color); color: var(--text-color); }
.status-failed { background-color: #6f42c1; color: var(--text-color); }
.status-closed { background-color: #6c757d; color: var(--text-color); }

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: var(--card-background);
    margin: 10% auto;
    padding: 2rem;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    position: relative;
    box-shadow: var(--shadow-hover);
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.close:hover {
    color: var(--danger-color);
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-tabs {
        flex-direction: column;
    }
    
    .content-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .cart-summary {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .order-item {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .products-grid {
        grid-template-columns: 1fr;
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OMS - Order Management System</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>Order Management System</h1>
            <div class="user-info">
                <span>Welcome, Admin</span>
            </div>
        </header>

        <!-- Navigation Tabs -->
        <nav class="nav-tabs">
            <button class="tab-btn active" data-tab="new-orders">New Orders</button>
            <button class="tab-btn" data-tab="open-orders">Open Orders</button>
            <button class="tab-btn" data-tab="received-orders">Received Orders</button>
            <button class="tab-btn" data-tab="rejected-orders">Rejected Orders</button>
            <button class="tab-btn" data-tab="failed-orders">Failed Orders</button>
            <button class="tab-btn" data-tab="closed-orders">Closed Orders</button>
        </nav>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- New Orders Tab -->
            <div id="new-orders" class="tab-content active">
                <div class="content-header">
                    <h2>New Orders</h2>
                    <button class="btn-primary" id="add-to-cart-btn">Add Item to Cart</button>
                </div>
                
                <!-- Cart Section -->
                <div class="cart-section">
                    <h3>Shopping Cart</h3>
                    <div class="cart-items" id="cart-items">
                        <!-- Cart items will be dynamically added here -->
                    </div>
                    <div class="cart-summary">
                        <div class="cart-total">
                            <span>Total: $<span id="cart-total">0.00</span></span>
                        </div>
                        <button class="btn-primary" id="proceed-to-order-btn" disabled>Proceed to Order</button>
                    </div>
                </div>

                <!-- Available Products -->
                <div class="products-section">
                    <h3>Available Products</h3>
                    <div class="products-grid" id="products-grid">
                        <!-- Products will be dynamically loaded here -->
                    </div>
                </div>
            </div>

            <!-- Open Orders Tab -->
            <div id="open-orders" class="tab-content">
                <div class="content-header">
                    <h2>Open Orders</h2>
                    <div class="filters">
                        <input type="text" placeholder="Search orders..." class="search-input">
                        <select class="filter-select">
                            <option value="">All Priorities</option>
                            <option value="high">High</option>
                            <option value="medium">Medium</option>
                            <option value="low">Low</option>
                        </select>
                    </div>
                </div>
                <div class="orders-list" id="open-orders-list">
                    <!-- Orders will be dynamically loaded here -->
                </div>
            </div>

            <!-- Received Orders Tab -->
            <div id="received-orders" class="tab-content">
                <div class="content-header">
                    <h2>Received Orders</h2>
                    <div class="filters">
                        <input type="text" placeholder="Search orders..." class="search-input">
                        <input type="date" class="date-filter">
                    </div>
                </div>
                <div class="orders-list" id="received-orders-list">
                    <!-- Orders will be dynamically loaded here -->
                </div>
            </div>

            <!-- Rejected Orders Tab -->
            <div id="rejected-orders" class="tab-content">
                <div class="content-header">
                    <h2>Rejected Orders</h2>
                    <div class="filters">
                        <input type="text" placeholder="Search orders..." class="search-input">
                        <select class="filter-select">
                            <option value="">All Reasons</option>
                            <option value="payment">Payment Issue</option>
                            <option value="inventory">Out of Stock</option>
                            <option value="validation">Validation Error</option>
                        </select>
                    </div>
                </div>
                <div class="orders-list" id="rejected-orders-list">
                    <!-- Orders will be dynamically loaded here -->
                </div>
            </div>

            <!-- Failed Orders Tab -->
            <div id="failed-orders" class="tab-content">
                <div class="content-header">
                    <h2>Failed Orders</h2>
                    <div class="filters">
                        <input type="text" placeholder="Search orders..." class="search-input">
                        <select class="filter-select">
                            <option value="">All Error Types</option>
                            <option value="system">System Error</option>
                            <option value="network">Network Error</option>
                            <option value="timeout">Timeout</option>
                        </select>
                    </div>
                </div>
                <div class="orders-list" id="failed-orders-list">
                    <!-- Orders will be dynamically loaded here -->
                </div>
            </div>

            <!-- Closed Orders Tab -->
            <div id="closed-orders" class="tab-content">
                <div class="content-header">
                    <h2>Closed Orders</h2>
                    <div class="filters">
                        <input type="text" placeholder="Search orders..." class="search-input">
                        <input type="date" class="date-filter">
                        <select class="filter-select">
                            <option value="">All Statuses</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                </div>
                <div class="orders-list" id="closed-orders-list">
                    <!-- Orders will be dynamically loaded here -->
                </div>
            </div>
        </main>
    </div>

    <!-- Modal for Add Item -->
    <div id="add-item-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>Add Item to Cart</h3>
            <form id="add-item-form">
                <div class="form-group">
                    <label for="item-name">Item Name:</label>
                    <input type="text" id="item-name" required>
                </div>
                <div class="form-group">
                    <label for="item-price">Price:</label>
                    <input type="number" id="item-price" step="0.01" required>
                </div>
                <div class="form-group">
                    <label for="item-quantity">Quantity:</label>
                    <input type="number" id="item-quantity" min="1" value="1" required>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary" id="cancel-add-item">Cancel</button>
                    <button type="submit" class="btn-primary">Add to Cart</button>
                </div>
            </form>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>

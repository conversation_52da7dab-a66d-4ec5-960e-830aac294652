// OMS Application JavaScript

class OMSApp {
    constructor() {
        this.cart = [];
        this.orders = {
            'new-orders': [],
            'open-orders': [],
            'received-orders': [],
            'rejected-orders': [],
            'failed-orders': [],
            'closed-orders': []
        };
        this.products = [
            { id: 1, name: 'Laptop Computer', price: 999.99, category: 'Electronics' },
            { id: 2, name: 'Office Chair', price: 299.99, category: 'Furniture' },
            { id: 3, name: 'Wireless Mouse', price: 49.99, category: 'Electronics' },
            { id: 4, name: '<PERSON><PERSON> Lamp', price: 79.99, category: 'Furniture' },
            { id: 5, name: 'Notebook Set', price: 19.99, category: 'Stationery' },
            { id: 6, name: 'USB Cable', price: 15.99, category: 'Electronics' }
        ];
        
        this.init();
        this.loadSampleData();
    }

    init() {
        this.setupEventListeners();
        this.renderProducts();
        this.updateCartDisplay();
        this.renderOrders();
    }

    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });

        // Cart functionality
        document.getElementById('add-to-cart-btn').addEventListener('click', () => this.showAddItemModal());
        document.getElementById('proceed-to-order-btn').addEventListener('click', () => this.proceedToOrder());

        // Modal functionality
        document.getElementById('add-item-form').addEventListener('submit', (e) => this.handleAddItem(e));
        document.getElementById('cancel-add-item').addEventListener('click', () => this.hideAddItemModal());
        document.querySelector('.close').addEventListener('click', () => this.hideAddItemModal());

        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('add-item-modal');
            if (e.target === modal) {
                this.hideAddItemModal();
            }
        });

        // Search and filter functionality
        document.querySelectorAll('.search-input').forEach(input => {
            input.addEventListener('input', (e) => this.handleSearch(e));
        });

        document.querySelectorAll('.filter-select').forEach(select => {
            select.addEventListener('change', (e) => this.handleFilter(e));
        });
    }

    switchTab(tabId) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
        document.getElementById(tabId).classList.add('active');

        // Render orders for the active tab
        this.renderOrders(tabId);
    }

    showAddItemModal() {
        document.getElementById('add-item-modal').style.display = 'block';
    }

    hideAddItemModal() {
        document.getElementById('add-item-modal').style.display = 'none';
        document.getElementById('add-item-form').reset();
    }

    handleAddItem(e) {
        e.preventDefault();
        const formData = new FormData(e.target);
        const item = {
            id: Date.now(),
            name: formData.get('item-name') || document.getElementById('item-name').value,
            price: parseFloat(formData.get('item-price') || document.getElementById('item-price').value),
            quantity: parseInt(formData.get('item-quantity') || document.getElementById('item-quantity').value)
        };

        this.addToCart(item);
        this.hideAddItemModal();
    }

    addToCart(item) {
        const existingItem = this.cart.find(cartItem => cartItem.name === item.name);
        
        if (existingItem) {
            existingItem.quantity += item.quantity;
        } else {
            this.cart.push(item);
        }

        this.updateCartDisplay();
    }

    removeFromCart(itemId) {
        this.cart = this.cart.filter(item => item.id !== itemId);
        this.updateCartDisplay();
    }

    updateQuantity(itemId, change) {
        const item = this.cart.find(item => item.id === itemId);
        if (item) {
            item.quantity += change;
            if (item.quantity <= 0) {
                this.removeFromCart(itemId);
            } else {
                this.updateCartDisplay();
            }
        }
    }

    updateCartDisplay() {
        const cartItemsContainer = document.getElementById('cart-items');
        const cartTotal = document.getElementById('cart-total');
        const proceedBtn = document.getElementById('proceed-to-order-btn');

        if (this.cart.length === 0) {
            cartItemsContainer.innerHTML = '<p style="text-align: center; color: #666; padding: 2rem;">Your cart is empty</p>';
            cartTotal.textContent = '0.00';
            proceedBtn.disabled = true;
            return;
        }

        let total = 0;
        cartItemsContainer.innerHTML = this.cart.map(item => {
            const itemTotal = item.price * item.quantity;
            total += itemTotal;
            
            return `
                <div class="cart-item">
                    <div class="cart-item-info">
                        <div class="cart-item-name">${item.name}</div>
                        <div class="cart-item-details">$${item.price.toFixed(2)} each</div>
                    </div>
                    <div class="cart-item-actions">
                        <div class="quantity-controls">
                            <button class="quantity-btn" onclick="app.updateQuantity(${item.id}, -1)">-</button>
                            <span class="quantity-display">${item.quantity}</span>
                            <button class="quantity-btn" onclick="app.updateQuantity(${item.id}, 1)">+</button>
                        </div>
                        <div style="margin-left: 1rem; font-weight: 600;">$${itemTotal.toFixed(2)}</div>
                        <button class="remove-btn" onclick="app.removeFromCart(${item.id})">Remove</button>
                    </div>
                </div>
            `;
        }).join('');

        cartTotal.textContent = total.toFixed(2);
        proceedBtn.disabled = false;
    }

    proceedToOrder() {
        if (this.cart.length === 0) return;

        const order = {
            id: `ORD-${Date.now()}`,
            items: [...this.cart],
            total: this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0),
            status: 'open',
            createdAt: new Date(),
            customer: 'Current User',
            priority: 'medium'
        };

        this.orders['open-orders'].push(order);
        this.cart = [];
        this.updateCartDisplay();

        // Switch to open orders tab
        this.switchTab('open-orders');

        // Show success message
        this.showNotification('Order created successfully!', 'success');
    }

    renderProducts() {
        const productsGrid = document.getElementById('products-grid');
        productsGrid.innerHTML = this.products.map(product => `
            <div class="product-card">
                <div class="product-name">${product.name}</div>
                <div class="product-price">$${product.price.toFixed(2)}</div>
                <button class="btn-primary" onclick="app.addProductToCart(${product.id})">Add to Cart</button>
            </div>
        `).join('');
    }

    addProductToCart(productId) {
        const product = this.products.find(p => p.id === productId);
        if (product) {
            this.addToCart({
                id: Date.now(),
                name: product.name,
                price: product.price,
                quantity: 1
            });
        }
    }

    renderOrders(activeTab = null) {
        const tabs = activeTab ? [activeTab] : Object.keys(this.orders);
        
        tabs.forEach(tab => {
            const container = document.getElementById(`${tab}-list`);
            if (!container) return;

            const orders = this.orders[tab];
            
            if (orders.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; padding: 2rem;">No orders found</p>';
                return;
            }

            container.innerHTML = orders.map(order => `
                <div class="order-item">
                    <div class="order-info">
                        <div class="order-id">${order.id}</div>
                        <div class="order-details">
                            Customer: ${order.customer} | 
                            Total: $${order.total.toFixed(2)} | 
                            Date: ${order.createdAt.toLocaleDateString()}
                            ${order.priority ? ` | Priority: ${order.priority}` : ''}
                            ${order.reason ? ` | Reason: ${order.reason}` : ''}
                        </div>
                    </div>
                    <div class="order-status status-${order.status}">${order.status}</div>
                </div>
            `).join('');
        });
    }

    loadSampleData() {
        // Sample orders for demonstration
        const sampleOrders = [
            { id: 'ORD-001', customer: 'John Doe', total: 299.99, status: 'open', createdAt: new Date(2024, 0, 15), priority: 'high' },
            { id: 'ORD-002', customer: 'Jane Smith', total: 149.99, status: 'received', createdAt: new Date(2024, 0, 14) },
            { id: 'ORD-003', customer: 'Bob Johnson', total: 599.99, status: 'rejected', createdAt: new Date(2024, 0, 13), reason: 'Payment Issue' },
            { id: 'ORD-004', customer: 'Alice Brown', total: 89.99, status: 'failed', createdAt: new Date(2024, 0, 12), reason: 'System Error' },
            { id: 'ORD-005', customer: 'Charlie Wilson', total: 199.99, status: 'closed', createdAt: new Date(2024, 0, 11) }
        ];

        // Distribute sample orders to appropriate categories
        sampleOrders.forEach(order => {
            const category = order.status === 'open' ? 'open-orders' : 
                           order.status === 'received' ? 'received-orders' :
                           order.status === 'rejected' ? 'rejected-orders' :
                           order.status === 'failed' ? 'failed-orders' :
                           'closed-orders';
            this.orders[category].push(order);
        });
    }

    handleSearch(e) {
        const searchTerm = e.target.value.toLowerCase();
        const activeTab = document.querySelector('.tab-content.active').id;

        if (activeTab && this.orders[activeTab]) {
            this.renderFilteredOrders(activeTab, searchTerm);
        }
    }

    handleFilter(e) {
        const filterValue = e.target.value;
        const activeTab = document.querySelector('.tab-content.active').id;

        if (activeTab && this.orders[activeTab]) {
            this.renderFilteredOrders(activeTab, null, filterValue);
        }
    }

    renderFilteredOrders(tab, searchTerm = null, filterValue = null) {
        const container = document.getElementById(`${tab}-list`);
        if (!container) return;

        let filteredOrders = [...this.orders[tab]];

        // Apply search filter
        if (searchTerm) {
            filteredOrders = filteredOrders.filter(order =>
                order.id.toLowerCase().includes(searchTerm) ||
                order.customer.toLowerCase().includes(searchTerm) ||
                order.total.toString().includes(searchTerm)
            );
        }

        // Apply category filter
        if (filterValue) {
            filteredOrders = filteredOrders.filter(order => {
                switch (tab) {
                    case 'open-orders':
                        return order.priority === filterValue;
                    case 'rejected-orders':
                        return order.reason && order.reason.toLowerCase().includes(filterValue.toLowerCase());
                    case 'failed-orders':
                        return order.reason && order.reason.toLowerCase().includes(filterValue.toLowerCase());
                    case 'closed-orders':
                        return order.status === filterValue;
                    default:
                        return true;
                }
            });
        }

        // Render filtered results
        if (filteredOrders.length === 0) {
            container.innerHTML = '<p style="text-align: center; color: #666; padding: 2rem;">No orders found matching your criteria</p>';
            return;
        }

        container.innerHTML = filteredOrders.map(order => `
            <div class="order-item">
                <div class="order-info">
                    <div class="order-id">${order.id}</div>
                    <div class="order-details">
                        Customer: ${order.customer} |
                        Total: $${order.total.toFixed(2)} |
                        Date: ${order.createdAt.toLocaleDateString()}
                        ${order.priority ? ` | Priority: ${order.priority}` : ''}
                        ${order.reason ? ` | Reason: ${order.reason}` : ''}
                    </div>
                </div>
                <div class="order-status status-${order.status}">${order.status}</div>
            </div>
        `).join('');
    }

    // Method to change order status (for demonstration)
    changeOrderStatus(orderId, newStatus) {
        // Find the order in all categories
        for (let category in this.orders) {
            const orderIndex = this.orders[category].findIndex(order => order.id === orderId);
            if (orderIndex !== -1) {
                const order = this.orders[category][orderIndex];

                // Remove from current category
                this.orders[category].splice(orderIndex, 1);

                // Update status and add to new category
                order.status = newStatus;
                const newCategory = newStatus === 'open' ? 'open-orders' :
                                  newStatus === 'received' ? 'received-orders' :
                                  newStatus === 'rejected' ? 'rejected-orders' :
                                  newStatus === 'failed' ? 'failed-orders' :
                                  'closed-orders';

                this.orders[newCategory].push(order);

                // Re-render all order lists
                this.renderOrders();
                break;
            }
        }
    }

    // Method to export orders (for demonstration)
    exportOrders(format = 'json') {
        const allOrders = Object.values(this.orders).flat();

        if (format === 'json') {
            const dataStr = JSON.stringify(allOrders, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = 'orders_export.json';
            link.click();

            URL.revokeObjectURL(url);
        }
    }

    // Method to get order statistics
    getOrderStats() {
        const stats = {};
        for (let category in this.orders) {
            stats[category] = this.orders[category].length;
        }
        return stats;
    }

    showNotification(message, type = 'info') {
        // Create and show notification
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 2rem;
            background-color: var(--success-color);
            color: white;
            border-radius: 4px;
            z-index: 1001;
            box-shadow: var(--shadow-hover);
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Initialize the application
const app = new OMSApp();
